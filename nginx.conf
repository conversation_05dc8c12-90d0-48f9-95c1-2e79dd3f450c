events {
    worker_connections 1024;
}

http {
    upstream auth_service {
        server auth-user-service:3001;
    }

    upstream course_service {
        server course-learning-service:3002;
    }

    upstream payment_service {
        server payment-subscription-service:3003;
    }

    upstream analytics_service {
        server reporting-analytics-service:3004;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=webhook:10m rate=1r/s;

    server {
        listen 80;
        server_name localhost;

        # Enable gzip compression
        gzip on;
        gzip_types text/plain application/json application/javascript text/css application/xml text/xml;

        # Security headers
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Auth & User Service routes
        location /api/auth/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://auth_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/users/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://auth_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/tutors/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://auth_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Course & Learning Service routes
        location /api/courses/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://course_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/lessons/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://course_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/assignments/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://course_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/progress/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://course_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/coaching/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://course_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Payment & Subscription Service routes
        location /api/payments/ {
            limit_req zone=api burst=10 nodelay;
            proxy_pass http://payment_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/subscriptions/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://payment_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Webhook endpoints (special rate limiting)
        location /api/webhooks/ {
            limit_req zone=webhook burst=5 nodelay;
            proxy_pass http://payment_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Analytics & Reporting Service routes
        location /api/analytics/ {
            limit_req zone=api burst=10 nodelay;
            proxy_pass http://analytics_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/reports/ {
            limit_req zone=api burst=5 nodelay;
            proxy_pass http://analytics_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Default 404 for unmatched routes
        location / {
            return 404 '{"success": false, "message": "API endpoint not found"}';
            add_header Content-Type application/json;
        }
    }
}