# Database Configuration
MONGODB_ROOT_USERNAME=admin
MONGODB_ROOT_PASSWORD=your_secure_password

# RabbitMQ Configuration
RABBITMQ_USERNAME=admin
RABBITMQ_PASSWORD=your_secure_password

# Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_FIREBASE_PRIVATE_KEY\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# Xendit Payment Gateway
XENDIT_API_KEY=your_xendit_api_key
XENDIT_WEBHOOK_TOKEN=your_xendit_webhook_token

# Bunny.net CDN Configuration
BUNNY_NET_API_KEY=your_bunnynet_api_key
BUNNY_NET_LIBRARY_ID=your_bunnynet_library_id
BUNNY_NET_STREAM_URL=https://your-bunnynet-stream-url.com

# Application URLs
FRONTEND_URL=https://your-frontend-domain.com

# Service URLs (for production inter-service communication)
AUTH_SERVICE_URL=http://auth-user-service:3001
COURSE_SERVICE_URL=http://course-learning-service:3002
PAYMENT_SERVICE_URL=http://payment-subscription-service:3003
ANALYTICS_SERVICE_URL=http://reporting-analytics-service:3004