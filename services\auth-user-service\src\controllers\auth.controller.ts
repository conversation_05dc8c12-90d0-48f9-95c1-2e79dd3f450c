import { Request, Response } from "express";
import { UserModel } from "../models/User.model";
import { AuthenticatedRequest } from "@shared/middleware/auth";
import { rabbitMQ } from "@shared/utils/rabbitmq";
import { UserCreatedEvent, ApiResponse } from "@shared/types";
import <PERSON><PERSON> from "joi";

export class AuthController {
  // Registration schema validation
  private registerSchema = Joi.object({
    firebaseUid: Joi.string().required(),
    email: Joi.string().email().required(),
    displayName: Joi.string().min(2).max(100).required(),
    photoURL: Joi.string().uri().optional(),
  });

  async register(req: Request, res: Response) {
    try {
      // Validate request body
      const { error, value } = this.registerSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: error.details.map((detail) => detail.message),
        } as ApiResponse);
      }

      const { firebaseUid, email, displayName, photoURL } = value;

      // Check if user already exists
      const existingUser = await UserModel.findOne({
        $or: [{ firebaseUid }, { email }],
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: "User already exists",
        } as ApiResponse);
      }

      // Create new user (default role: student)
      const newUser = new UserModel({
        firebaseUid,
        email,
        displayName,
        photoURL,
        role: "student",
        isActive: true,
      });

      await newUser.save();

      // Publish user creation event
      const userCreatedEvent: UserCreatedEvent = {
        type: "USER_CREATED",
        data: newUser.toObject(),
        timestamp: new Date(),
      };

      await rabbitMQ.publishEvent(
        "user.events",
        "user.created",
        userCreatedEvent
      );

      return res.status(201).json({
        success: true,
        message: "User registered successfully",
        data: newUser,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Registration error:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to register user",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async login(req: Request, res: Response) {
    try {
      const { firebaseUid } = req.body;

      if (!firebaseUid) {
        return res.status(400).json({
          success: false,
          message: "Firebase UID is required",
        } as ApiResponse);
      }

      // Find user by Firebase UID
      const user = await UserModel.findOne({ firebaseUid, isActive: true });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found or inactive",
        } as ApiResponse);
      }

      return res.json({
        success: true,
        message: "Login successful",
        data: user,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Login error:", error);
      return res.status(500).json({
        success: false,
        message: "Login failed",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getProfile(req: AuthenticatedRequest, res: Response) {
    try {
      const user = await UserModel.findOne({
        firebaseUid: req.user!.uid,
        isActive: true,
      }).populate("subscriptions");

      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        } as ApiResponse);
      }

      return res.json({
        success: true,
        message: "Profile retrieved successfully",
        data: user,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get profile error:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to retrieve profile",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async updateProfile(req: AuthenticatedRequest, res: Response) {
    try {
      const updateSchema = Joi.object({
        displayName: Joi.string().min(2).max(100).optional(),
        photoURL: Joi.string().uri().optional(),
      });

      const { error, value } = updateSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: error.details.map((detail) => detail.message),
        } as ApiResponse);
      }

      const user = await UserModel.findOneAndUpdate(
        { firebaseUid: req.user!.uid, isActive: true },
        { $set: value },
        { new: true, runValidators: true }
      );

      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        } as ApiResponse);
      }

      return res.json({
        success: true,
        message: "Profile updated successfully",
        data: user,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Update profile error:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to update profile",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async deleteAccount(req: AuthenticatedRequest, res: Response) {
    try {
      const user = await UserModel.findOneAndUpdate(
        { firebaseUid: req.user!.uid },
        { $set: { isActive: false } },
        { new: true }
      );

      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        } as ApiResponse);
      }

      return res.json({
        success: true,
        message: "Account deactivated successfully",
      } as ApiResponse);
    } catch (error: any) {
      console.error("Delete account error:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to delete account",
        errors: [error.message],
      } as ApiResponse);
    }
  }
}
