import mongoose, { Document, Schema } from 'mongoose';

export interface AnalyticsDocument extends Document {
  date: Date;
  type: 'daily' | 'weekly' | 'monthly';
  
  // User metrics
  totalUsers: number;
  newUsers: number;
  activeUsers: number;
  studentCount: number;
  tutorCount: number;
  
  // Course metrics
  totalCourses: number;
  publishedCourses: number;
  newCourses: number;
  totalEnrollments: number;
  newEnrollments: number;
  
  // Learning metrics
  totalAssignments: number;
  completedAssignments: number;
  failedAssignments: number;
  averageScore: number;
  
  // Subscription metrics
  activeSubscriptions: number;
  expiredSubscriptions: number;
  terminatedSubscriptions: number;
  
  // Payment metrics
  totalRevenue: number;
  completedPayments: number;
  failedPayments: number;
  averageOrderValue: number;
  
  // Performance metrics
  completionRate: number;
  failureRate: number;
  retentionRate: number;
  
  createdAt: Date;
  updatedAt: Date;
}

const AnalyticsSchema = new Schema<AnalyticsDocument>(
  {
    date: { type: Date, required: true, index: true },
    type: { type: String, enum: ['daily', 'weekly', 'monthly'], required: true },
    
    // User metrics
    totalUsers: { type: Number, default: 0 },
    newUsers: { type: Number, default: 0 },
    activeUsers: { type: Number, default: 0 },
    studentCount: { type: Number, default: 0 },
    tutorCount: { type: Number, default: 0 },
    
    // Course metrics
    totalCourses: { type: Number, default: 0 },
    publishedCourses: { type: Number, default: 0 },
    newCourses: { type: Number, default: 0 },
    totalEnrollments: { type: Number, default: 0 },
    newEnrollments: { type: Number, default: 0 },
    
    // Learning metrics
    totalAssignments: { type: Number, default: 0 },
    completedAssignments: { type: Number, default: 0 },
    failedAssignments: { type: Number, default: 0 },
    averageScore: { type: Number, default: 0 },
    
    // Subscription metrics
    activeSubscriptions: { type: Number, default: 0 },
    expiredSubscriptions: { type: Number, default: 0 },
    terminatedSubscriptions: { type: Number, default: 0 },
    
    // Payment metrics
    totalRevenue: { type: Number, default: 0 },
    completedPayments: { type: Number, default: 0 },
    failedPayments: { type: Number, default: 0 },
    averageOrderValue: { type: Number, default: 0 },
    
    // Performance metrics
    completionRate: { type: Number, default: 0 },
    failureRate: { type: Number, default: 0 },
    retentionRate: { type: Number, default: 0 },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v;
        return ret;
      }
    }
  }
);

// Unique index for date and type
AnalyticsSchema.index({ date: 1, type: 1 }, { unique: true });

export const AnalyticsModel = mongoose.model<AnalyticsDocument>('Analytics', AnalyticsSchema);