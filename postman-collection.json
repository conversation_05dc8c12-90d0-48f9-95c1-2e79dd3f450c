{
  "info": {
    "name": "Time Course Platform API",
    "description": "Complete API collection for Time Course - English Online Course Platform with Microservices Architecture",
    "version": "1.0.0",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8080",
      "description": "API Gateway URL (Development)"
    },
    {
      "key": "prodUrl",
      "value": "https://your-domain.com",
      "description": "Production API URL"
    },
    {
      "key": "authToken",
      "value": "",
      "description": "Firebase Auth Token"
    },
    {
      "key": "userId",
      "value": "",
      "description": "Current User ID"
    },
    {
      "key": "courseId",
      "value": "",
      "description": "Course ID for testing"
    },
    {
      "key": "assignmentId",
      "value": "",
      "description": "Assignment ID for testing"
    },
    {
      "key": "paymentId",
      "value": "",
      "description": "Payment ID for testing"
    },
    {
      "key": "subscriptionId",
      "value": "",
      "description": "Subscription ID for testing"
    }
  ],
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{authToken}}",
        "type": "string"
      }
    ]
  },
  "item": [
    {
      "name": "Health Checks",
      "item": [
        {
          "name": "API Gateway Health",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{baseUrl}}/health",
              "host": ["{{baseUrl}}"],
              "path": ["health"]
            }
          },
          "response": []
        }
      ]
    },
    {
      "name": "Auth & User Service",
      "item": [
        {
          "name": "Authentication",
          "item": [
            {
              "name": "Register User",
              "request": {
                "method": "POST",
                "header": [
                  {
                    "key": "Content-Type",
                    "value": "application/json"
                  }
                ],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"firebaseUid\": \"firebase_uid_123\",\n  \"email\": \"<EMAIL>\",\n  \"displayName\": \"John Doe\",\n  \"photoURL\": \"https://example.com/photo.jpg\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/auth/register",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "auth", "register"]
                }
              },
              "response": []
            },
            {
              "name": "Login User",
              "request": {
                "method": "POST",
                "header": [
                  {
                    "key": "Content-Type",
                    "value": "application/json"
                  }
                ],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"firebaseUid\": \"firebase_uid_123\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/auth/login",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "auth", "login"]
                }
              },
              "response": []
            },
            {
              "name": "Get Profile",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/auth/profile",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "auth", "profile"]
                }
              },
              "response": []
            },
            {
              "name": "Update Profile",
              "request": {
                "method": "PUT",
                "header": [
                  {
                    "key": "Content-Type",
                    "value": "application/json"
                  },
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"displayName\": \"John Smith\",\n  \"photoURL\": \"https://example.com/new-photo.jpg\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/auth/profile",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "auth", "profile"]
                }
              },
              "response": []
            }
          ]
        },
        {
          "name": "User Management (Admin)",
          "item": [
            {
              "name": "Get All Users",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/users?page=1&limit=10&role=student&search=john",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "users"],
                  "query": [
                    {
                      "key": "page",
                      "value": "1"
                    },
                    {
                      "key": "limit",
                      "value": "10"
                    },
                    {
                      "key": "role",
                      "value": "student"
                    },
                    {
                      "key": "search",
                      "value": "john"
                    }
                  ]
                }
              },
              "response": []
            },
            {
              "name": "Get User by ID",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/users/{{userId}}",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "users", "{{userId}}"]
                }
              },
              "response": []
            }
          ]
        },
        {
          "name": "Tutor Management (Admin)",
          "item": [
            {
              "name": "Create Tutor",
              "request": {
                "method": "POST",
                "header": [
                  {
                    "key": "Content-Type",
                    "value": "application/json"
                  },
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"firebaseUid\": \"tutor_firebase_uid_123\",\n  \"email\": \"<EMAIL>\",\n  \"displayName\": \"Jane Teacher\",\n  \"photoURL\": \"https://example.com/tutor-photo.jpg\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/tutors",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "tutors"]
                }
              },
              "response": []
            },
            {
              "name": "Get All Tutors",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/tutors",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "tutors"]
                }
              },
              "response": []
            }
          ]
        }
      ]
    },
    {
      "name": "Course & Learning Service",
      "item": [
        {
          "name": "Courses",
          "item": [
            {
              "name": "Get Course Catalog (Public)",
              "request": {
                "method": "GET",
                "header": [],
                "url": {
                  "raw": "{{baseUrl}}/api/courses?page=1&limit=10&category=IELTS",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "courses"],
                  "query": [
                    {
                      "key": "page",
                      "value": "1"
                    },
                    {
                      "key": "limit",
                      "value": "10"
                    },
                    {
                      "key": "category",
                      "value": "IELTS"
                    }
                  ]
                }
              },
              "response": []
            },
            {
              "name": "Create Course (Tutor)",
              "request": {
                "method": "POST",
                "header": [
                  {
                    "key": "Content-Type",
                    "value": "application/json"
                  },
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"title\": \"IELTS Preparation Course\",\n  \"description\": \"Complete IELTS preparation course covering all four skills: Reading, Writing, Listening, and Speaking.\",\n  \"category\": \"IELTS\",\n  \"duration\": 90,\n  \"price\": 500000,\n  \"currency\": \"IDR\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/courses",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "courses"]
                }
              },
              "response": []
            }
          ]
            },
            {
              "name": "Get Course Details",
              "request": {
                "method": "GET",
                "header": [],
                "url": {
                  "raw": "{{baseUrl}}/api/courses/{{courseId}}",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "courses", "{{courseId}}"]
                }
              },
              "response": []
            },
            {
              "name": "Update Course (Tutor)",
              "request": {
                "method": "PUT",
                "header": [
                  {
                    "key": "Content-Type",
                    "value": "application/json"
                  },
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"title\": \"Advanced IELTS Preparation Course\",\n  \"description\": \"Updated course description\",\n  \"price\": 600000\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/courses/{{courseId}}",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "courses", "{{courseId}}"]
                }
              },
              "response": []
            },
            {
              "name": "Publish Course (Tutor)",
              "request": {
                "method": "PUT",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/courses/{{courseId}}/publish",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "courses", "{{courseId}}", "publish"]
                }
              },
              "response": []
            }
          ]
        },
        {
          "name": "Assignments",
          "item": [
            {
              "name": "Submit Assignment (Student)",
              "request": {
                "method": "POST",
                "header": [
                  {
                    "key": "Content-Type",
                    "value": "application/json"
                  },
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"assignmentId\": \"assignment_id_123\",\n  \"answers\": [\n    {\n      \"questionId\": \"q1\",\n      \"answer\": \"My answer to question 1\"\n    },\n    {\n      \"questionId\": \"q2\",\n      \"answer\": \"My answer to question 2\"\n    }\n  ]\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/assignments/submit",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "assignments", "submit"]
                }
              },
              "response": []
            },
            {
              "name": "Get Assignment Result (Student)",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/assignments/{{assignmentId}}/result",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "assignments", "{{assignmentId}}", "result"]
                }
              },
              "response": []
            }
          ]
        },
        {
          "name": "Coaching",
          "item": [
            {
              "name": "Get Available Coaching Sessions",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/coaching/available",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "coaching", "available"]
                }
              },
              "response": []
            },
            {
              "name": "Book Coaching Session (Student)",
              "request": {
                "method": "POST",
                "header": [
                  {
                    "key": "Content-Type",
                    "value": "application/json"
                  },
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"sessionId\": \"session_id_123\",\n  \"preferredTime\": \"2024-01-15T10:00:00Z\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/coaching/book",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "coaching", "book"]
                }
              },
              "response": []
            }
          ]
        }
      ]
    },
    {
      "name": "Payment & Subscription Service",
      "item": [
        {
          "name": "Payments",
          "item": [
            {
              "name": "Create Payment (Student)",
              "request": {
                "method": "POST",
                "header": [
                  {
                    "key": "Content-Type",
                    "value": "application/json"
                  },
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"courseId\": \"course_id_123\",\n  \"amount\": 500000,\n  \"currency\": \"IDR\",\n  \"paymentMethod\": \"BANK_TRANSFER\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/payments/create",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "payments", "create"]
                }
              },
              "response": []
            },
            {
              "name": "Get My Payments (Student)",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/payments/my-payments?page=1&limit=10",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "payments", "my-payments"],
                  "query": [
                    {
                      "key": "page",
                      "value": "1"
                    },
                    {
                      "key": "limit",
                      "value": "10"
                    }
                  ]
                }
              },
              "response": []
            },
            {
              "name": "Get Payment by ID (Student)",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/payments/{{paymentId}}",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "payments", "{{paymentId}}"]
                }
              },
              "response": []
            }
          ]
        },
        {
          "name": "Subscriptions",
          "item": [
            {
              "name": "Get My Subscriptions (Student)",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/subscriptions/my-subscriptions",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "subscriptions", "my-subscriptions"]
                }
              },
              "response": []
            },
            {
              "name": "Get Subscription Progress (Student)",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/subscriptions/{{subscriptionId}}/progress",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "subscriptions", "{{subscriptionId}}", "progress"]
                }
              },
              "response": []
            }
          ]
        },
        {
          "name": "Webhooks",
          "item": [
            {
              "name": "Xendit Webhook",
              "request": {
                "method": "POST",
                "header": [
                  {
                    "key": "Content-Type",
                    "value": "application/json"
                  },
                  {
                    "key": "x-callback-token",
                    "value": "your-xendit-callback-token"
                  }
                ],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"id\": \"payment_id_123\",\n  \"status\": \"SUCCEEDED\",\n  \"amount\": 500000,\n  \"currency\": \"IDR\",\n  \"reference_id\": \"COURSE_course_id_123_1640995200000\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/webhooks/xendit",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "webhooks", "xendit"]
                }
              },
              "response": []
            }
          ]
        }
      ]
    },
    {
      "name": "Analytics & Reporting Service",
      "item": [
        {
          "name": "Analytics",
          "item": [
            {
              "name": "Get Dashboard Analytics (Admin)",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/analytics/dashboard?period=daily&days=30",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "analytics", "dashboard"],
                  "query": [
                    {
                      "key": "period",
                      "value": "daily"
                    },
                    {
                      "key": "days",
                      "value": "30"
                    }
                  ]
                }
              },
              "response": []
            },
            {
              "name": "Get User Analytics (Admin)",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/analytics/users?period=daily&days=30",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "analytics", "users"],
                  "query": [
                    {
                      "key": "period",
                      "value": "daily"
                    },
                    {
                      "key": "days",
                      "value": "30"
                    }
                  ]
                }
              },
              "response": []
            },
            {
              "name": "Get Revenue Analytics (Admin)",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/analytics/revenue?period=monthly&days=90",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "analytics", "revenue"],
                  "query": [
                    {
                      "key": "period",
                      "value": "monthly"
                    },
                    {
                      "key": "days",
                      "value": "90"
                    }
                  ]
                }
              },
              "response": []
            }
          ]
        },
        {
          "name": "Reports",
          "item": [
            {
              "name": "Get Platform Overview Report (Admin)",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/reports/platform-overview",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "reports", "platform-overview"]
                }
              },
              "response": []
            },
            {
              "name": "Get Tutor Performance Report (Tutor)",
              "request": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{authToken}}"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/reports/tutor/performance",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "reports", "tutor", "performance"]
                }
              },
              "response": []
            }
          ]
        }
      ]
    }
  ]
}
