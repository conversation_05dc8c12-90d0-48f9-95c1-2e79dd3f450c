# Time Course Platform - Postman API Testing Guide

This guide provides comprehensive instructions for testing the Time Course Platform APIs using Postman.

## 📁 Files Included

- `postman-collection.json` - Complete API collection with all endpoints
- `postman-environment-dev.json` - Development environment variables
- `postman-environment-prod.json` - Production environment variables

## 🚀 Quick Setup

### 1. Import Collection and Environments

1. Open Postman
2. Click **Import** button
3. Import all three files:
   - `postman-collection.json`
   - `postman-environment-dev.json`
   - `postman-environment-prod.json`

### 2. Select Environment

- For development testing: Select **"Time Course - Development"** environment
- For production testing: Select **"Time Course - Production"** environment

### 3. Start Development Services

```bash
# Start all services in development mode
npm run dev

# Or using Docker Compose directly
docker-compose -f docker-compose.dev.yml up --build
```

The API Gateway will be available at `http://localhost:8080`

## 🔐 Authentication Setup

### Firebase Authentication

The platform uses Firebase Authentication. You need to:

1. **Get Firebase Auth Token:**
   - Use Firebase SDK in your frontend application
   - Or use Firebase REST API to authenticate
   - Copy the ID token

2. **Set Auth Token in Postman:**
   - Go to your environment (Dev/Prod)
   - Set the `authToken` variable with your Firebase ID token
   - The collection is configured to use Bearer token authentication

### Example Firebase Auth Token Request

```bash
# Using Firebase REST API
curl -X POST \
  'https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "returnSecureToken": true
  }'
```

## 📋 API Testing Workflow

### 1. Health Checks

Start by testing the health endpoints to ensure all services are running:

- **API Gateway Health**: `GET /health`

### 2. User Registration & Authentication

1. **Register New User**: `POST /api/auth/register`
   ```json
   {
     "firebaseUid": "firebase_uid_123",
     "email": "<EMAIL>",
     "displayName": "John Doe",
     "photoURL": "https://example.com/photo.jpg"
   }
   ```

2. **Login User**: `POST /api/auth/login`
   ```json
   {
     "firebaseUid": "firebase_uid_123"
   }
   ```

3. **Get Profile**: `GET /api/auth/profile` (requires auth token)

### 3. Course Management

1. **View Course Catalog**: `GET /api/courses` (public endpoint)
2. **Create Course** (Tutor only): `POST /api/courses`
3. **Update Course** (Tutor only): `PUT /api/courses/{id}`
4. **Publish Course** (Tutor only): `PUT /api/courses/{id}/publish`

### 4. Payment Flow

1. **Create Payment**: `POST /api/payments/create`
2. **Check Payment Status**: `GET /api/payments/{paymentId}`
3. **View Payment History**: `GET /api/payments/my-payments`

### 5. Analytics & Reporting (Admin only)

1. **Dashboard Analytics**: `GET /api/analytics/dashboard`
2. **User Analytics**: `GET /api/analytics/users`
3. **Revenue Analytics**: `GET /api/analytics/revenue`

## 🎭 User Roles & Permissions

### Student Role
- Register/Login
- View course catalog
- Enroll in courses
- Submit assignments
- Book coaching sessions
- Make payments
- View subscriptions

### Tutor Role
- All student permissions
- Create and manage courses
- Create lessons and assignments
- Conduct coaching sessions
- View performance reports

### Admin Role
- All permissions
- User management
- Tutor approval
- Platform analytics
- System reports

## 🔧 Environment Variables

### Development Environment
- `baseUrl`: `http://localhost:8080`
- `authToken`: Your Firebase ID token
- `userId`: Auto-populated after login
- `courseId`: Set after creating a course
- `paymentId`: Set after creating a payment

### Production Environment
- `baseUrl`: `https://api.timecourse.com`
- Other variables same as development

## 📊 Response Format

All API responses follow this standard format:

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": { /* response data */ }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error messages"]
}
```

### Paginated Response
```json
{
  "success": true,
  "message": "Data retrieved successfully",
  "data": [/* array of items */],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

## 🧪 Testing Scenarios

### 1. Complete User Journey
1. Register as student
2. Browse courses
3. Create payment for course
4. Submit assignment
5. Book coaching session

### 2. Tutor Workflow
1. Register as tutor (admin approval required)
2. Create course
3. Add lessons and assignments
4. Publish course
5. View performance reports

### 3. Admin Operations
1. Approve tutors
2. Manage users
3. View analytics
4. Generate reports

## 🚨 Common Issues & Solutions

### 1. Authentication Errors
- **Issue**: 401 Unauthorized
- **Solution**: Ensure `authToken` is set and valid

### 2. Service Not Available
- **Issue**: Connection refused
- **Solution**: Check if Docker services are running

### 3. Validation Errors
- **Issue**: 400 Bad Request
- **Solution**: Check request body format against schema

### 4. Permission Denied
- **Issue**: 403 Forbidden
- **Solution**: Ensure user has correct role permissions

## 📝 Notes

- All timestamps are in ISO 8601 format
- Amounts are in smallest currency unit (e.g., cents for USD, rupiah for IDR)
- File uploads use multipart/form-data
- Rate limiting is applied (10 requests/second for most endpoints)
- Webhook endpoints have special rate limiting (1 request/second)

## 🔗 Related Documentation

- [API Documentation](./docs/api-documentation.md)
- [Development Guide](./docs/development-continuation.md)
- [Deployment Guide](./docs/deployment-guide.md)
