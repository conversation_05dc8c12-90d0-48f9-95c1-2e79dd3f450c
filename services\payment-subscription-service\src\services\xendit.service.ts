import axios from 'axios';

interface XenditPaymentRequest {
  amount: number;
  currency: string;
  reference_id: string;
  description: string;
  customer_id: string;
  payment_method: string;
  success_redirect_url: string;
  failure_redirect_url: string;
}

export class XenditService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.XENDIT_API_KEY || '';
    this.baseUrl = process.env.XENDIT_BASE_URL || 'https://api.xendit.co';
    
    if (!this.apiKey) {
      throw new Error('XENDIT_API_KEY is required');
    }
  }

  async createPayment(paymentData: XenditPaymentRequest) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/v2/invoices`,
        {
          external_id: paymentData.reference_id,
          amount: paymentData.amount,
          currency: paymentData.currency,
          description: paymentData.description,
          customer: {
            given_names: 'Student',
            email: `${paymentData.customer_id}@timecourse.com`, // This should be actual student email
          },
          success_redirect_url: paymentData.success_redirect_url,
          failure_redirect_url: paymentData.failure_redirect_url,
          payment_methods: [paymentData.payment_method],
        },
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(this.apiKey + ':').toString('base64')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        id: response.data.id,
        external_id: response.data.external_id,
        amount: response.data.amount,
        status: response.data.status,
        actions: {
          desktop_web_checkout_url: response.data.invoice_url,
          mobile_web_checkout_url: response.data.invoice_url
        }
      };

    } catch (error: any) {
      console.error('Xendit API Error:', error.response?.data || error.message);
      throw new Error('Failed to create payment with Xendit');
    }
  }

  async getPaymentStatus(paymentId: string) {
    try {
      const response = await axios.get(
        `${this.baseUrl}/v2/invoices/${paymentId}`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(this.apiKey + ':').toString('base64')}`
          }
        }
      );

      return {
        id: response.data.id,
        status: response.data.status,
        amount: response.data.amount,
        paid_at: response.data.paid_at
      };

    } catch (error: any) {
      console.error('Xendit Get Payment Error:', error.response?.data || error.message);
      throw new Error('Failed to get payment status from Xendit');
    }
  }
}