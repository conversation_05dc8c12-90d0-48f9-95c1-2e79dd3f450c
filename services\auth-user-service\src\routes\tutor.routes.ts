import express from "express";
import { <PERSON><PERSON><PERSON><PERSON>roller } from "../controllers/tutor.controller";
import { authenticateToken, requireRole } from "@shared/middleware/auth";

const router = express.Router();
const tutorController = new TutorController();

// Admin only routes for tutor management
router.post(
  "/",
  authenticateToken,
  requireRole(["admin"]),
  tutorController.createTutor
);
router.get(
  "/",
  authenticateToken,
  requireRole(["admin"]),
  tutorController.getAllTutors
);
router.put(
  "/:id/approve",
  authenticateToken,
  requireRole(["admin"]),
  tutorController.approveTutor
);
router.put(
  "/:id/deactivate",
  authenticateToken,
  requireRole(["admin"]),
  tutorController.deactivateTutor
);

export default router;
