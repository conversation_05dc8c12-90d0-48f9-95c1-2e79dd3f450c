# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY services/course-learning-service/package*.json ./
COPY services/course-learning-service/tsconfig.docker.json ./tsconfig.json

# Copy shared dependencies
COPY shared ./shared

# Install all dependencies (including dev dependencies for building)
RUN npm ci

# Copy source code
COPY services/course-learning-service/src ./src

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Copy package files
COPY services/course-learning-service/package*.json ./

# Copy shared dependencies
COPY shared ./shared

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3002/health || exit 1

# Start the service
CMD ["npm", "start"]