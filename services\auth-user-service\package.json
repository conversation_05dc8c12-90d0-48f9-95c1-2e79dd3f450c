{"name": "auth-user-service", "version": "1.0.0", "description": "Authentication and User Management Service", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest"}, "dependencies": {"amqplib": "^0.10.3", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^13.5.0", "helmet": "^7.0.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.3", "morgan": "^1.10.0"}, "devDependencies": {"@types/amqplib": "^0.10.1", "@types/bcryptjs": "^2.4.4", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.5", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.3", "@types/morgan": "^1.9.4", "@types/node": "^20.8.6", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}