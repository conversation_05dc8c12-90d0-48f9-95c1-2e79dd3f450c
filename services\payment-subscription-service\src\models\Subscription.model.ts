import mongoose, { Document, Schema, Types } from "mongoose";
import {
  Subscription,
  SubscriptionStatus,
  Progress,
  AssignmentResult,
  StudentAnswer,
} from "@shared/types";

export interface SubscriptionDocument
  extends Omit<Subscription, "_id" | "courseId" | "paymentId">,
    Document {
  courseId: Types.ObjectId;
  paymentId: Types.ObjectId;
  checkFailureTermination(): boolean;
  calculateExpectedProgress(totalModules: number): number;
}

const StudentAnswerSchema = new Schema<StudentAnswer>({
  questionId: { type: String, required: true },
  answer: { type: String, required: true },
  isCorrect: { type: Boolean, required: true },
  points: { type: Number, required: true, default: 0 },
});

const AssignmentResultSchema = new Schema<AssignmentResult>({
  assignmentId: { type: String, required: true },
  lessonId: { type: String, required: true },
  score: { type: Number, required: true },
  totalPoints: { type: Number, required: true },
  percentage: { type: Number, required: true },
  passed: { type: Boolean, required: true },
  submittedAt: { type: Date, required: true },
  answers: [StudentAnswerSchema],
});

const ProgressSchema = new Schema<Progress>({
  completedModules: [{ type: String }],
  completedLessons: [{ type: String }],
  assignmentResults: [AssignmentResultSchema],
  overallProgress: { type: Number, default: 0, min: 0, max: 100 },
  isPaceBehind: { type: Boolean, default: false },
  expectedProgress: { type: Number, default: 0, min: 0, max: 100 },
});

const SubscriptionSchema = new Schema<SubscriptionDocument>(
  {
    studentId: {
      type: String,
      required: true,
      index: true,
    },
    courseId: {
      type: Schema.Types.ObjectId,
      required: true,
      index: true,
    },
    status: {
      type: String,
      enum: ["active", "expired", "terminated"],
      required: true,
      default: "active",
    },
    startDate: {
      type: Date,
      required: true,
      default: Date.now,
    },
    endDate: {
      type: Date,
      required: true,
    },
    failureCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    progress: {
      type: ProgressSchema,
      default: () => ({
        completedModules: [],
        completedLessons: [],
        assignmentResults: [],
        overallProgress: 0,
        isPaceBehind: false,
        expectedProgress: 0,
      }),
    },
    paymentId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Payment",
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (_doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes
SubscriptionSchema.index({ studentId: 1, status: 1 });
SubscriptionSchema.index({ courseId: 1 });
SubscriptionSchema.index({ endDate: 1 }); // For expiration checking
SubscriptionSchema.index({ status: 1, failureCount: 1 }); // For terminated subscriptions

// Check if subscription should be terminated due to failures
SubscriptionSchema.methods.checkFailureTermination = function () {
  if (this.failureCount >= 3 && this.status === "active") {
    this.status = "terminated";
    return true;
  }
  return false;
};

// Calculate expected progress based on course duration and days passed
SubscriptionSchema.methods.calculateExpectedProgress = function (
  _totalModules: number
) {
  const courseDurationMs = this.endDate.getTime() - this.startDate.getTime();
  const timePassed = Date.now() - this.startDate.getTime();
  const progressRatio = Math.min(timePassed / courseDurationMs, 1);

  this.progress.expectedProgress = Math.round(progressRatio * 100);
  this.progress.isPaceBehind =
    this.progress.overallProgress < this.progress.expectedProgress;

  return this.progress.expectedProgress;
};

export const SubscriptionModel = mongoose.model<SubscriptionDocument>(
  "Subscription",
  SubscriptionSchema
);
