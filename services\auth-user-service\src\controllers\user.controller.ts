import { Request, Response } from "express";
import { UserModel } from "../models/User.model";
import { AuthenticatedRequest } from "@shared/middleware/auth";
import { ApiResponse, PaginatedResponse } from "@shared/types";

export class UserController {
  async getAllUsers(req: AuthenticatedRequest, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const role = req.query.role as string;
      const search = req.query.search as string;

      const filter: any = {};

      if (role) {
        filter.role = role;
      }

      if (search) {
        filter.$or = [
          { displayName: { $regex: search, $options: "i" } },
          { email: { $regex: search, $options: "i" } },
        ];
      }

      const skip = (page - 1) * limit;

      const [users, total] = await Promise.all([
        UserModel.find(filter).skip(skip).limit(limit).sort({ createdAt: -1 }),
        UserModel.countDocuments(filter),
      ]);

      const response: PaginatedResponse<(typeof users)[0]> = {
        success: true,
        message: "Users retrieved successfully",
        data: users,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };

      res.json(response);
    } catch (error: any) {
      console.error("Get all users error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve users",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getUserById(req: Request, res: Response) {
    try {
      const user = await UserModel.findById(req.params.id).populate(
        "subscriptions"
      );

      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        } as ApiResponse);
      }

      return res.json({
        success: true,
        message: "User retrieved successfully",
        data: user,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get user by ID error:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to retrieve user",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async updateUser(req: Request, res: Response) {
    try {
      const { isActive, role } = req.body;

      const user = await UserModel.findByIdAndUpdate(
        req.params.id,
        { $set: { isActive, role } },
        { new: true, runValidators: true }
      );

      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        } as ApiResponse);
      }

      return res.json({
        success: true,
        message: "User updated successfully",
        data: user,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Update user error:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to update user",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async deleteUser(req: Request, res: Response) {
    try {
      const user = await UserModel.findByIdAndUpdate(
        req.params.id,
        { $set: { isActive: false } },
        { new: true }
      );

      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        } as ApiResponse);
      }

      return res.json({
        success: true,
        message: "User deactivated successfully",
      } as ApiResponse);
    } catch (error: any) {
      console.error("Delete user error:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to delete user",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getFailedStudents(req: Request, res: Response) {
    try {
      // This would need to query the subscription service for failed students
      // For now, we'll return a placeholder response
      return res.json({
        success: true,
        message: "Failed students retrieved successfully",
        data: [], // This should be populated from subscription data
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get failed students error:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to retrieve failed students",
        errors: [error.message],
      } as ApiResponse);
    }
  }
}
