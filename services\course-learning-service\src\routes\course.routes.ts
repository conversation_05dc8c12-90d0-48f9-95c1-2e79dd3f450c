import express from "express";
import { CourseController } from "../controllers/course.controller";
import { authenticateToken, requireRole } from "@shared/middleware/auth";

const router = express.Router();
const courseController = new CourseController();

// Public routes
router.get("/", courseController.getAllCourses); // Course catalog
router.get("/:id", courseController.getCourseById);

// Tutor routes
router.post(
  "/",
  authenticateToken,
  requireRole(["tutor", "admin"]),
  courseController.createCourse
);
router.put(
  "/:id",
  authenticateToken,
  requireRole(["tutor", "admin"]),
  courseController.updateCourse
);
router.delete(
  "/:id",
  authenticateToken,
  requireRole(["tutor", "admin"]),
  courseController.deleteCourse
);
router.put(
  "/:id/publish",
  authenticateToken,
  requireRole(["tutor", "admin"]),
  courseController.publishCourse
);
router.put(
  "/:id/unpublish",
  authenticateToken,
  requireRole(["tutor", "admin"]),
  courseController.unpublishCourse
);

// Get tutor's courses
router.get(
  "/tutor/my-courses",
  authenticateToken,
  requireRole(["tutor"]),
  courseController.getTutorCourses
);

// Admin routes
router.get(
  "/admin/all",
  authenticateToken,
  requireRole(["admin"]),
  courseController.getAllCoursesAdmin
);
router.put(
  "/admin/:id/approve",
  authenticateToken,
  requireRole(["admin"]),
  courseController.approveCourse
);

export default router;
