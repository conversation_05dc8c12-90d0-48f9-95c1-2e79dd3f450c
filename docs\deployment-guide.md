# Panduan Deployment Time Course Platform ke VPS

Panduan ini menjelaskan langkah-langkah untuk melakukan deployment Time Course Platform ke VPS (Virtual Private Server) menggunakan Docker dan Docker Compose.

## 📋 Persyaratan Sistem

### Spesifikasi VPS Minimum
- **CPU:** 4 vCPU cores
- **RAM:** 8 GB
- **Storage:** 100 GB SSD
- **OS:** Ubuntu 20.04 LTS atau Ubuntu 22.04 LTS
- **Network:** Bandwidth unlimited dengan port 80, 443 terbuka

### Software Requirements
- Docker Engine 20.10+
- Docker Compose 2.0+
- Git
- Nginx (untuk reverse proxy production)
- SSL Certificate (Let's Encrypt/Certbot)

## 🛠️ Persiapan Server

### 1. Update System
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y curl wget git vim ufw
```

### 2. Install Docker
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker --version
docker-compose --version
```

### 3. Setup Firewall
```bash
# Enable UFW
sudo ufw enable

# Allow SSH
sudo ufw allow 22/tcp

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Check status
sudo ufw status
```

### 4. Optimize System for Production
```bash
# Increase file descriptor limits
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# Increase virtual memory for MongoDB
echo "vm.max_map_count=262144" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Create swap file if needed (untuk server dengan RAM < 8GB)
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

## 📁 Setup Project

### 1. Clone Repository
```bash
# Create project directory
sudo mkdir -p /opt/timecourse
sudo chown $USER:$USER /opt/timecourse
cd /opt/timecourse

# Clone repository
git clone https://github.com/your-username/time-course-platform.git .
```

### 2. Setup Environment Variables
```bash
# Copy environment template
cp .env.example .env

# Edit environment file
nano .env
```

**Isi konfigurasi berikut:**
```bash
# Database Configuration
MONGODB_ROOT_USERNAME=timecourse_admin
MONGODB_ROOT_PASSWORD=your_very_secure_password_here

# RabbitMQ Configuration
RABBITMQ_USERNAME=timecourse_queue
RABBITMQ_PASSWORD=another_secure_password_here

# Firebase Configuration (dapatkan dari Firebase Console)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour_Firebase_Private_Key_Here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Xendit Payment Gateway (dapatkan dari Xendit Dashboard)
XENDIT_API_KEY=xnd_development_or_live_key_here
XENDIT_WEBHOOK_TOKEN=your_webhook_verification_token

# Bunny.net CDN Configuration (dapatkan dari Bunny.net Dashboard)
BUNNY_NET_API_KEY=your-bunnynet-api-key
BUNNY_NET_LIBRARY_ID=your-library-id-number
BUNNY_NET_STREAM_URL=https://your-stream-zone.b-cdn.net

# Application URLs
FRONTEND_URL=https://yourdomain.com
```

### 3. Setup SSL Certificate (Production)
```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot certonly --standalone -d yourdomain.com -d api.yourdomain.com

# Create SSL directory for Docker
sudo mkdir -p /opt/timecourse/ssl
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem /opt/timecourse/ssl/
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem /opt/timecourse/ssl/
sudo chown -R $USER:$USER /opt/timecourse/ssl
```

### 4. Create Production Nginx Configuration
```bash
# Create production nginx config
cp nginx.conf nginx.prod.conf
nano nginx.prod.conf
```

**Update nginx.prod.conf:**
```nginx
events {
    worker_connections 1024;
}

http {
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    # SSL Configuration
    ssl_certificate /etc/ssl/certs/fullchain.pem;
    ssl_certificate_key /etc/ssl/certs/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    
    # Upstream services
    upstream auth_service {
        server auth-user-service:3001;
    }
    # ... (same as nginx.conf but with SSL)
    
    # HTTPS Server
    server {
        listen 443 ssl http2;
        server_name yourdomain.com api.yourdomain.com;
        
        # Security headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        
        # Your location blocks here...
    }
    
    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name yourdomain.com api.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }
}
```

## 🚀 Deployment

### 1. Build dan Start Services
```bash
cd /opt/timecourse

# Build all images
docker-compose -f docker-compose.prod.yml build

# Start services
docker-compose -f docker-compose.prod.yml up -d

# Check if all services are running
docker-compose -f docker-compose.prod.yml ps
```

### 2. Verify Deployment
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs -f

# Test health endpoints
curl http://localhost/health
curl http://localhost:3001/health  # Auth service
curl http://localhost:3002/health  # Course service
curl http://localhost:3003/health  # Payment service
curl http://localhost:3004/health  # Analytics service
```

### 3. Setup Database Indexes (First Time Only)
```bash
# Connect to MongoDB and create indexes
docker exec -it timecourse-mongodb-auth-prod mongo -u timecourse_admin -p your_password --authenticationDatabase admin

# In MongoDB shell, create indexes for better performance
use timecourse_auth
db.users.createIndex({ "firebaseUid": 1 })
db.users.createIndex({ "email": 1 })
db.users.createIndex({ "role": 1 })

# Repeat for other databases...
```

## 🔧 Maintenance

### 1. Update Application
```bash
cd /opt/timecourse

# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d
```

### 2. Database Backup
```bash
# Create backup directory
sudo mkdir -p /backup/timecourse

# Create backup script
cat > /backup/timecourse/backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/timecourse"

# Backup all MongoDB databases
docker exec timecourse-mongodb-auth-prod mongodump --out /data/backup --authenticationDatabase admin -u timecourse_admin -p your_password
docker exec timecourse-mongodb-courses-prod mongodump --out /data/backup --authenticationDatabase admin -u timecourse_admin -p your_password
docker exec timecourse-mongodb-payments-prod mongodump --out /data/backup --authenticationDatabase admin -u timecourse_admin -p your_password
docker exec timecourse-mongodb-analytics-prod mongodump --out /data/backup --authenticationDatabase admin -u timecourse_admin -p your_password

# Copy backups to host
docker cp timecourse-mongodb-auth-prod:/data/backup $BACKUP_DIR/mongodb_$DATE
docker cp timecourse-mongodb-courses-prod:/data/backup $BACKUP_DIR/mongodb_$DATE
docker cp timecourse-mongodb-payments-prod:/data/backup $BACKUP_DIR/mongodb_$DATE
docker cp timecourse-mongodb-analytics-prod:/data/backup $BACKUP_DIR/mongodb_$DATE

# Compress backup
tar -czf $BACKUP_DIR/timecourse_backup_$DATE.tar.gz $BACKUP_DIR/mongodb_$DATE
rm -rf $BACKUP_DIR/mongodb_$DATE

# Keep only last 7 days of backups
find $BACKUP_DIR -name "timecourse_backup_*.tar.gz" -mtime +7 -delete

echo "Backup completed: timecourse_backup_$DATE.tar.gz"
EOF

# Make script executable
chmod +x /backup/timecourse/backup.sh

# Setup daily backup cron job
(crontab -l 2>/dev/null; echo "0 2 * * * /backup/timecourse/backup.sh") | crontab -
```

### 3. Log Rotation
```bash
# Setup log rotation for Docker containers
sudo cat > /etc/logrotate.d/docker-containers << 'EOF'
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size 1M
    missingok
    delaycompress
    copytruncate
}
EOF
```

### 4. Monitoring Script
```bash
# Create monitoring script
cat > /opt/timecourse/monitor.sh << 'EOF'
#!/bin/bash

# Check if all services are running
SERVICES=("timecourse-auth-service-prod" "timecourse-course-service-prod" "timecourse-payment-service-prod" "timecourse-analytics-service-prod")

for service in "${SERVICES[@]}"; do
    if ! docker ps | grep -q $service; then
        echo "ERROR: $service is not running!"
        # Send notification (email, slack, etc.)
        # docker-compose -f docker-compose.prod.yml up -d $service
    else
        echo "OK: $service is running"
    fi
done

# Check disk space
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "WARNING: Disk usage is at ${DISK_USAGE}%"
fi

# Check memory usage
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
    echo "WARNING: Memory usage is at ${MEMORY_USAGE}%"
fi
EOF

chmod +x /opt/timecourse/monitor.sh

# Run monitoring every 5 minutes
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/timecourse/monitor.sh") | crontab -
```

## 🔒 Security Best Practices

### 1. Secure MongoDB
```bash
# Use strong passwords
# Enable authentication
# Limit network access to local services only
# Regular security updates
```

### 2. Secure API Gateway
```bash
# Use SSL/TLS certificates
# Implement rate limiting
# Add security headers
# Regular Nginx updates
```

### 3. Regular Updates
```bash
# Create update script
cat > /opt/timecourse/update.sh << 'EOF'
#!/bin/bash
echo "Updating system packages..."
sudo apt update && sudo apt upgrade -y

echo "Updating Docker images..."
cd /opt/timecourse
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

echo "Cleaning up old images..."
docker image prune -f

echo "Update completed!"
EOF

chmod +x /opt/timecourse/update.sh

# Schedule weekly updates
(crontab -l 2>/dev/null; echo "0 3 * * 0 /opt/timecourse/update.sh") | crontab -
```

## 🆘 Troubleshooting

### Common Issues

1. **Service tidak bisa start:**
   ```bash
   # Check logs
   docker-compose -f docker-compose.prod.yml logs servicename
   
   # Check disk space
   df -h
   
   # Check memory
   free -h
   ```

2. **Database connection error:**
   ```bash
   # Restart MongoDB services
   docker-compose -f docker-compose.prod.yml restart mongodb-auth mongodb-courses mongodb-payments mongodb-analytics
   ```

3. **High memory usage:**
   ```bash
   # Check top processes
   docker stats
   
   # Restart services if needed
   docker-compose -f docker-compose.prod.yml restart
   ```

4. **SSL Certificate renewal:**
   ```bash
   # Renew certificates
   sudo certbot renew
   
   # Copy new certificates
   sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem /opt/timecourse/ssl/
   sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem /opt/timecourse/ssl/
   
   # Restart API Gateway
   docker-compose -f docker-compose.prod.yml restart api-gateway
   ```

### Emergency Recovery

```bash
# Stop all services
docker-compose -f docker-compose.prod.yml down

# Restore from backup
cd /backup/timecourse
tar -xzf latest_backup.tar.gz

# Restore databases (example)
# mongorestore --host mongodb-auth --authenticationDatabase admin -u username -p password /path/to/backup

# Start services
docker-compose -f docker-compose.prod.yml up -d
```

## 📞 Support

Jika mengalami masalah saat deployment:
1. Periksa logs dengan `docker-compose logs`
2. Pastikan semua environment variables sudah benar
3. Cek koneksi internet dan DNS
4. Verifikasi firewall settings
5. Hubungi tim development untuk bantuan lebih lanjut

---

**Catatan:** Panduan ini diasumsikan untuk deployment production. Pastikan semua konfigurasi keamanan sudah diterapkan dengan benar sebelum go-live.