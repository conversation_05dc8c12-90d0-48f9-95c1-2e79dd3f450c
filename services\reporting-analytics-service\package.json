{"name": "reporting-analytics-service", "version": "1.0.0", "description": "Reporting and Analytics Service", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.6.3", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "amqplib": "^0.10.3", "joi": "^17.11.0", "axios": "^1.5.1", "node-cron": "^3.0.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/node": "^20.8.6", "@types/amqplib": "^0.10.1", "@types/joi": "^17.2.3", "@types/node-cron": "^3.0.8", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "@types/jest": "^29.5.5"}}