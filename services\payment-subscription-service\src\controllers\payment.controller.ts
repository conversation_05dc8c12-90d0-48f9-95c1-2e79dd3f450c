import { Request, Response } from "express";
import { PaymentModel } from "../models/Payment.model";
import { AuthenticatedRequest } from "@shared/middleware/auth";
import { ApiResponse, PaginatedResponse } from "@shared/types";
import { XenditService } from "../services/xendit.service";
import Jo<PERSON> from "joi";

export class PaymentController {
  private xenditService = new XenditService();

  private createPaymentSchema = Joi.object({
    courseId: Joi.string().required(),
    amount: Joi.number().min(1000).required(), // Minimum IDR 1000
    currency: Joi.string().default("IDR"),
    paymentMethod: Joi.string().required(),
  });

  async createPayment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { error, value } = this.createPaymentSchema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: "Validation error",
          errors: error.details.map((detail) => detail.message),
        } as ApiResponse);
        return;
      }

      const { courseId, amount, currency, paymentMethod } = value;
      const studentId = req.user!.uid;

      // Create payment request with Xendit
      const xenditPayment = await this.xenditService.createPayment({
        amount,
        currency,
        reference_id: `COURSE_${courseId}_${Date.now()}`,
        description: `Course enrollment payment for course ${courseId}`,
        customer_id: studentId,
        payment_method: paymentMethod,
        success_redirect_url: `${process.env.FRONTEND_URL}/payment/success`,
        failure_redirect_url: `${process.env.FRONTEND_URL}/payment/failed`,
      });

      // Save payment record
      const payment = new PaymentModel({
        studentId,
        courseId,
        amount,
        currency,
        paymentMethod,
        xenditPaymentId: xenditPayment.id,
        status: "pending",
      });

      await payment.save();

      res.status(201).json({
        success: true,
        message: "Payment created successfully",
        data: {
          paymentId: payment._id,
          xenditPaymentUrl: xenditPayment.actions.desktop_web_checkout_url,
          amount,
          currency,
          status: "pending",
        },
      } as ApiResponse);
    } catch (error: any) {
      console.error("Create payment error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to create payment",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getMyPayments(req: AuthenticatedRequest, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const skip = (page - 1) * limit;

      const [payments, total] = await Promise.all([
        PaymentModel.find({ studentId: req.user!.uid })
          .skip(skip)
          .limit(limit)
          .sort({ createdAt: -1 }),
        PaymentModel.countDocuments({ studentId: req.user!.uid }),
      ]);

      const response: PaginatedResponse<(typeof payments)[0]> = {
        success: true,
        message: "Payments retrieved successfully",
        data: payments,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };

      res.json(response);
    } catch (error: any) {
      console.error("Get my payments error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve payments",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getPaymentById(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    try {
      const payment = await PaymentModel.findOne({
        _id: req.params.paymentId,
        studentId: req.user!.uid,
      });

      if (!payment) {
        res.status(404).json({
          success: false,
          message: "Payment not found",
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        message: "Payment retrieved successfully",
        data: payment,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get payment by ID error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve payment",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getAllPayments(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const status = req.query.status as string;

      const filter: any = {};
      if (status) {
        filter.status = status;
      }

      const skip = (page - 1) * limit;

      const [payments, total] = await Promise.all([
        PaymentModel.find(filter)
          .skip(skip)
          .limit(limit)
          .sort({ createdAt: -1 }),
        PaymentModel.countDocuments(filter),
      ]);

      const response: PaginatedResponse<(typeof payments)[0]> = {
        success: true,
        message: "All payments retrieved successfully",
        data: payments,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };

      res.json(response);
    } catch (error: any) {
      console.error("Get all payments error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve payments",
        errors: [error.message],
      } as ApiResponse);
    }
  }
}
