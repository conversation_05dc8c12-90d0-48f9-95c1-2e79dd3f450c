import express from "express";
import { ProgressController } from "../controllers/progress.controller";
import { authenticateToken, requireRole } from "@shared/middleware/auth";

const router = express.Router();
const progressController = new ProgressController();

// Student routes
router.get(
  "/my-progress",
  authenticateToken,
  requireRole(["student"]),
  progressController.getMyProgress
);
router.get(
  "/course/:courseId",
  authenticateToken,
  requireRole(["student"]),
  progressController.getCourseProgress
);

export default router;
