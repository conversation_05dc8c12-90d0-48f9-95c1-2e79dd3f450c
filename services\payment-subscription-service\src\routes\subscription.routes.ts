import express from "express";
import { SubscriptionController } from "../controllers/subscription.controller";
import { authenticateToken, requireRole } from "@shared/middleware/auth";

const router = express.Router();
const subscriptionController = new SubscriptionController();

// Student routes
router.get(
  "/my-subscriptions",
  authenticateToken,
  requireRole(["student"]),
  subscriptionController.getMySubscriptions
);
router.get(
  "/:subscriptionId",
  authenticateToken,
  requireRole(["student"]),
  subscriptionController.getSubscriptionById
);
router.get(
  "/:subscriptionId/progress",
  authenticateToken,
  requireRole(["student"]),
  subscriptionController.getSubscriptionProgress
);

// Admin routes
router.get(
  "/admin/all",
  authenticateToken,
  requireRole(["admin"]),
  subscriptionController.getAllSubscriptions
);
router.get(
  "/admin/failed-students",
  authenticateToken,
  requireRole(["admin", "tutor"]),
  subscriptionController.getFailedStudents
);
router.put(
  "/admin/:subscriptionId/status",
  authenticateToken,
  requireRole(["admin"]),
  subscriptionController.updateSubscriptionStatus
);

export default router;
