// Postman Test Scripts for Time Course Platform API
// Copy and paste these scripts into the "Tests" tab of your Postman requests

// =============================================================================
// COMMON TEST SCRIPTS
// =============================================================================

// Basic Response Validation
function validateBasicResponse() {
    pm.test("Status code is successful", function () {
        pm.expect(pm.response.code).to.be.oneOf([200, 201]);
    });

    pm.test("Response has success field", function () {
        pm.expect(pm.response.json()).to.have.property('success');
    });

    pm.test("Response has message field", function () {
        pm.expect(pm.response.json()).to.have.property('message');
    });

    pm.test("Response time is less than 2000ms", function () {
        pm.expect(pm.response.responseTime).to.be.below(2000);
    });
}

// Authentication Response Validation
function validateAuthResponse() {
    validateBasicResponse();
    
    pm.test("Response has data field", function () {
        pm.expect(pm.response.json()).to.have.property('data');
    });

    pm.test("User data contains required fields", function () {
        const userData = pm.response.json().data;
        pm.expect(userData).to.have.property('_id');
        pm.expect(userData).to.have.property('email');
        pm.expect(userData).to.have.property('displayName');
        pm.expect(userData).to.have.property('role');
    });

    // Store user ID for future requests
    if (pm.response.json().success) {
        pm.environment.set("userId", pm.response.json().data._id);
    }
}

// Paginated Response Validation
function validatePaginatedResponse() {
    validateBasicResponse();
    
    pm.test("Response has pagination field", function () {
        pm.expect(pm.response.json()).to.have.property('pagination');
    });

    pm.test("Pagination has required fields", function () {
        const pagination = pm.response.json().pagination;
        pm.expect(pagination).to.have.property('page');
        pm.expect(pagination).to.have.property('limit');
        pm.expect(pagination).to.have.property('total');
        pm.expect(pagination).to.have.property('totalPages');
    });
}

// Error Response Validation
function validateErrorResponse(expectedStatusCode) {
    pm.test(`Status code is ${expectedStatusCode}`, function () {
        pm.response.to.have.status(expectedStatusCode);
    });

    pm.test("Response has success field set to false", function () {
        pm.expect(pm.response.json().success).to.be.false;
    });

    pm.test("Response has error message", function () {
        pm.expect(pm.response.json()).to.have.property('message');
    });
}

// =============================================================================
// SPECIFIC ENDPOINT TESTS
// =============================================================================

// Health Check Test
function testHealthCheck() {
    pm.test("Status code is 200", function () {
        pm.response.to.have.status(200);
    });

    pm.test("Response contains 'healthy'", function () {
        pm.expect(pm.response.text()).to.include("healthy");
    });
}

// User Registration Test
function testUserRegistration() {
    validateAuthResponse();
    
    pm.test("User role is student by default", function () {
        pm.expect(pm.response.json().data.role).to.eql("student");
    });

    pm.test("User is active", function () {
        pm.expect(pm.response.json().data.isActive).to.be.true;
    });
}

// Course Creation Test
function testCourseCreation() {
    validateBasicResponse();
    
    pm.test("Course data contains required fields", function () {
        const courseData = pm.response.json().data;
        pm.expect(courseData).to.have.property('_id');
        pm.expect(courseData).to.have.property('title');
        pm.expect(courseData).to.have.property('description');
        pm.expect(courseData).to.have.property('category');
        pm.expect(courseData).to.have.property('price');
        pm.expect(courseData).to.have.property('tutorId');
    });

    pm.test("Course is not published by default", function () {
        pm.expect(pm.response.json().data.isPublished).to.be.false;
    });

    // Store course ID for future requests
    if (pm.response.json().success) {
        pm.environment.set("courseId", pm.response.json().data._id);
    }
}

// Payment Creation Test
function testPaymentCreation() {
    validateBasicResponse();
    
    pm.test("Payment data contains required fields", function () {
        const paymentData = pm.response.json().data;
        pm.expect(paymentData).to.have.property('paymentId');
        pm.expect(paymentData).to.have.property('xenditPaymentUrl');
        pm.expect(paymentData).to.have.property('amount');
        pm.expect(paymentData).to.have.property('status');
    });

    pm.test("Payment status is pending", function () {
        pm.expect(pm.response.json().data.status).to.eql("pending");
    });

    // Store payment ID for future requests
    if (pm.response.json().success) {
        pm.environment.set("paymentId", pm.response.json().data.paymentId);
    }
}

// Analytics Test
function testAnalytics() {
    validateBasicResponse();
    
    pm.test("Analytics data contains overview", function () {
        pm.expect(pm.response.json().data).to.have.property('overview');
    });

    pm.test("Analytics data contains trends", function () {
        pm.expect(pm.response.json().data).to.have.property('trends');
    });

    pm.test("Analytics data contains period", function () {
        pm.expect(pm.response.json().data).to.have.property('period');
    });
}

// =============================================================================
// USAGE EXAMPLES
// =============================================================================

/*
// Example 1: Health Check
testHealthCheck();

// Example 2: User Registration
testUserRegistration();

// Example 3: Course Creation
testCourseCreation();

// Example 4: Payment Creation
testPaymentCreation();

// Example 5: Get All Users (Admin)
validatePaginatedResponse();

// Example 6: Error Response (e.g., Unauthorized)
validateErrorResponse(401);

// Example 7: Analytics Dashboard
testAnalytics();

// Example 8: Custom validation
validateBasicResponse();
pm.test("Custom validation", function () {
    // Your custom test logic here
});
*/

// =============================================================================
// PRE-REQUEST SCRIPTS
// =============================================================================

/*
// Set dynamic variables (use in Pre-request Script tab)
pm.environment.set("timestamp", Date.now());
pm.environment.set("randomEmail", `test${Math.random().toString(36).substring(7)}@example.com`);

// Generate random course data
pm.environment.set("randomCourseTitle", `Test Course ${Math.random().toString(36).substring(7)}`);
pm.environment.set("randomPrice", Math.floor(Math.random() * 1000000) + 100000); // 100k - 1M IDR
*/
