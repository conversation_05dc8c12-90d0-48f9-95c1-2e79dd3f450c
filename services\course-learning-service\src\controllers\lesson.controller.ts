import { Response } from "express";
import { CourseModel } from "../models/Course.model";
import { AuthenticatedRequest } from "@shared/middleware/auth";
import { ApiResponse } from "@shared/types";
import <PERSON><PERSON> from "joi";

export class LessonController {
  private createLessonSchema = Joi.object({
    title: Joi.string().min(3).max(200).required(),
    description: Joi.string().min(10).max(1000).required(),
    order: Joi.number().min(1).required(),
    duration: Joi.number().min(1).required(), // minutes
  });

  async createLesson(req: AuthenticatedRequest, res: Response): Promise<any> {
    try {
      const { courseId, moduleId } = req.params;
      const { error, value } = this.createLessonSchema.validate(req.body);

      if (error) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: error.details.map((detail) => detail.message),
        } as ApiResponse);
      }

      const course = await CourseModel.findOne({
        _id: courseId,
        tutorId: req.user!.uid,
      });

      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found or unauthorized",
        } as ApiResponse);
      }

      const moduleIndex = course.modules.findIndex(
        (m) => m._id!.toString() === moduleId
      );
      if (moduleIndex === -1) {
        return res.status(404).json({
          success: false,
          message: "Module not found",
        } as ApiResponse);
      }

      const newLesson = {
        ...value,
        materials: [],
        assignments: [],
      };

      course.modules[moduleIndex].lessons.push(newLesson);
      await course.save();

      res.status(201).json({
        success: true,
        message: "Lesson created successfully",
        data: newLesson,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Create lesson error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to create lesson",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getLesson(req: AuthenticatedRequest, res: Response): Promise<any> {
    try {
      const { courseId, moduleId, lessonId } = req.params;

      const course = await CourseModel.findById(courseId);
      if (!course || !course.isPublished) {
        return res.status(404).json({
          success: false,
          message: "Course not found or not available",
        } as ApiResponse);
      }

      const module = course.modules.find((m) => m._id!.toString() === moduleId);
      if (!module) {
        return res.status(404).json({
          success: false,
          message: "Module not found",
        } as ApiResponse);
      }

      const lesson = module.lessons.find((l) => l._id!.toString() === lessonId);
      if (!lesson) {
        return res.status(404).json({
          success: false,
          message: "Lesson not found",
        } as ApiResponse);
      }

      // TODO: Check if student has access to this lesson based on subscription

      res.json({
        success: true,
        message: "Lesson retrieved successfully",
        data: lesson,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get lesson error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve lesson",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async updateLesson(req: AuthenticatedRequest, res: Response): Promise<any> {
    try {
      const { courseId, moduleId, lessonId } = req.params;
      const { error, value } = this.createLessonSchema.validate(req.body, {
        allowUnknown: true,
      });

      if (error) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: error.details.map((detail) => detail.message),
        } as ApiResponse);
      }

      const course = await CourseModel.findOne({
        _id: courseId,
        tutorId: req.user!.uid,
      });

      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found or unauthorized",
        } as ApiResponse);
      }

      const moduleIndex = course.modules.findIndex(
        (m) => m._id!.toString() === moduleId
      );
      if (moduleIndex === -1) {
        return res.status(404).json({
          success: false,
          message: "Module not found",
        } as ApiResponse);
      }

      const lessonIndex = course.modules[moduleIndex].lessons.findIndex(
        (l) => l._id!.toString() === lessonId
      );
      if (lessonIndex === -1) {
        return res.status(404).json({
          success: false,
          message: "Lesson not found",
        } as ApiResponse);
      }

      // Update lesson fields
      Object.assign(course.modules[moduleIndex].lessons[lessonIndex], value);
      await course.save();

      res.json({
        success: true,
        message: "Lesson updated successfully",
        data: course.modules[moduleIndex].lessons[lessonIndex],
      } as ApiResponse);
    } catch (error: any) {
      console.error("Update lesson error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update lesson",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async deleteLesson(req: AuthenticatedRequest, res: Response): Promise<any> {
    try {
      const { courseId, moduleId, lessonId } = req.params;

      const course = await CourseModel.findOne({
        _id: courseId,
        tutorId: req.user!.uid,
      });

      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found or unauthorized",
        } as ApiResponse);
      }

      const moduleIndex = course.modules.findIndex(
        (m) => m._id!.toString() === moduleId
      );
      if (moduleIndex === -1) {
        return res.status(404).json({
          success: false,
          message: "Module not found",
        } as ApiResponse);
      }

      const lessonIndex = course.modules[moduleIndex].lessons.findIndex(
        (l) => l._id!.toString() === lessonId
      );
      if (lessonIndex === -1) {
        return res.status(404).json({
          success: false,
          message: "Lesson not found",
        } as ApiResponse);
      }

      course.modules[moduleIndex].lessons.splice(lessonIndex, 1);
      await course.save();

      res.json({
        success: true,
        message: "Lesson deleted successfully",
      } as ApiResponse);
    } catch (error: any) {
      console.error("Delete lesson error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete lesson",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async uploadVideo(req: AuthenticatedRequest, res: Response) {
    try {
      // TODO: Implement Bunny.net video upload
      // This is a placeholder for video upload functionality
      res.json({
        success: true,
        message: "Video upload functionality to be implemented with Bunny.net",
        data: {
          videoUrl: "https://placeholder-video-url.com",
          videoId: "placeholder-video-id",
        },
      } as ApiResponse);
    } catch (error: any) {
      console.error("Upload video error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to upload video",
        errors: [error.message],
      } as ApiResponse);
    }
  }
}
