import { Request, Response } from "express";
import { AuthenticatedRequest } from "@shared/middleware/auth";
import { ApiResponse } from "@shared/types";
import { ReportsService } from "../services/reports.service";

export class ReportsController {
  private reportsService = new ReportsService();

  async getPlatformOverviewReport(req: Request, res: Response) {
    try {
      const startDate = new Date(req.query.startDate as string);
      const endDate = new Date(req.query.endDate as string);

      const report = await this.reportsService.generatePlatformOverviewReport(
        startDate,
        endDate
      );

      res.json({
        success: true,
        message: "Platform overview report generated successfully",
        data: report,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get platform overview report error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to generate platform overview report",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getCoursePerformanceReport(req: Request, res: Response) {
    try {
      const startDate = new Date(req.query.startDate as string);
      const endDate = new Date(req.query.endDate as string);

      const report = await this.reportsService.generateCoursePerformanceReport(
        startDate,
        endDate
      );

      res.json({
        success: true,
        message: "Course performance report generated successfully",
        data: report,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get course performance report error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to generate course performance report",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getFinancialReport(req: Request, res: Response) {
    try {
      const startDate = new Date(req.query.startDate as string);
      const endDate = new Date(req.query.endDate as string);

      const report = await this.reportsService.generateFinancialReport(
        startDate,
        endDate
      );

      res.json({
        success: true,
        message: "Financial report generated successfully",
        data: report,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get financial report error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to generate financial report",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getUserEngagementReport(req: Request, res: Response) {
    try {
      const startDate = new Date(req.query.startDate as string);
      const endDate = new Date(req.query.endDate as string);

      const report = await this.reportsService.generateUserEngagementReport(
        startDate,
        endDate
      );

      res.json({
        success: true,
        message: "User engagement report generated successfully",
        data: report,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get user engagement report error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to generate user engagement report",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getTutorPerformanceReport(req: AuthenticatedRequest, res: Response) {
    try {
      const startDate = new Date(req.query.startDate as string);
      const endDate = new Date(req.query.endDate as string);
      const tutorId = req.user!.uid;

      const report = await this.reportsService.generateTutorPerformanceReport(
        tutorId,
        startDate,
        endDate
      );

      res.json({
        success: true,
        message: "Tutor performance report generated successfully",
        data: report,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get tutor performance report error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to generate tutor performance report",
        errors: [error.message],
      } as ApiResponse);
    }
  }
}
