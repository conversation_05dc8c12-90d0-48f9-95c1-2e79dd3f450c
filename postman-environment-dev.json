{"id": "dev-environment-id", "name": "Time Course - Development", "values": [{"key": "baseUrl", "value": "http://localhost:8080", "description": "API Gateway URL for Development", "enabled": true}, {"key": "authToken", "value": "", "description": "Firebase Auth Token - Set this after authentication", "enabled": true}, {"key": "userId", "value": "", "description": "Current User ID - Auto-populated after login", "enabled": true}, {"key": "courseId", "value": "", "description": "Course ID for testing - Set after creating a course", "enabled": true}, {"key": "assignmentId", "value": "", "description": "Assignment ID for testing", "enabled": true}, {"key": "paymentId", "value": "", "description": "Payment ID for testing", "enabled": true}, {"key": "subscriptionId", "value": "", "description": "Subscription ID for testing", "enabled": true}, {"key": "tutorId", "value": "", "description": "Tutor ID for testing", "enabled": true}, {"key": "studentEmail", "value": "<EMAIL>", "description": "Test student email", "enabled": true}, {"key": "tutorEmail", "value": "<EMAIL>", "description": "Test tutor email", "enabled": true}, {"key": "adminEmail", "value": "<EMAIL>", "description": "Test admin email", "enabled": true}], "_postman_variable_scope": "environment"}