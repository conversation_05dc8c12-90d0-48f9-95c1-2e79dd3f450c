import express from "express";
import { AuthController } from "../controllers/auth.controller";
import { authenticateToken } from "@shared/middleware/auth";

const router = express.Router();
const authController = new AuthController();

// Public routes
router.post("/register", authController.register);
router.post("/login", authController.login);

// Protected routes
router.get("/profile", authenticateToken, authController.getProfile);
router.put("/profile", authenticateToken, authController.updateProfile);
router.delete("/account", authenticateToken, authController.deleteAccount);

export default router;
