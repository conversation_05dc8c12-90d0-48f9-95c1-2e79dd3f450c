import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import dotenv from "dotenv";
import mongoose from "mongoose";
import cron from "node-cron";
import { rabbitMQ } from "@shared/utils/rabbitmq";

// Import routes
import analyticsRoutes from "./routes/analytics.routes";
import reportsRoutes from "./routes/reports.routes";

// Import services
import { AnalyticsService } from "./services/analytics.service";

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3004;
const analyticsService = new AnalyticsService();

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan("combined"));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get("/health", (req, res) => {
  res.json({
    success: true,
    message: "Reporting & Analytics Service is healthy",
    timestamp: new Date().toISOString(),
  });
});

// Routes
app.use("/api/analytics", analyticsRoutes);
app.use("/api/reports", reportsRoutes);

// Error handling middleware
app.use(
  (
    err: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    console.error("Error:", err);
    res.status(err.status || 500).json({
      success: false,
      message: err.message || "Internal Server Error",
      ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
    });
  }
);

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Endpoint not found",
  });
});

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(
      process.env.MONGODB_URI ||
        "mongodb://localhost:27017/timecourse_analytics"
    );
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error("Database connection failed:", error);
    process.exit(1);
  }
};

// Setup cron jobs for data aggregation
const setupCronJobs = () => {
  // Run analytics aggregation every hour
  cron.schedule("0 * * * *", async () => {
    console.log("Running hourly analytics aggregation...");
    await analyticsService.aggregateHourlyData();
  });

  // Run daily analytics aggregation every day at midnight
  cron.schedule("0 0 * * *", async () => {
    console.log("Running daily analytics aggregation...");
    await analyticsService.aggregateDailyData();
  });

  // Generate weekly reports every Sunday at 6 AM
  cron.schedule("0 6 * * 0", async () => {
    console.log("Generating weekly reports...");
    await analyticsService.generateWeeklyReports();
  });

  console.log("Analytics cron jobs scheduled successfully");
};

// Start server
const startServer = async () => {
  try {
    await connectDB();
    await rabbitMQ.connect();

    // Subscribe to all events for analytics
    await rabbitMQ.subscribeToEvents(
      "user.events",
      "analytics.user.events",
      ["user.created", "user.updated"],
      async (data) => {
        await analyticsService.handleUserEvent(data);
      }
    );

    await rabbitMQ.subscribeToEvents(
      "learning.events",
      "analytics.learning.events",
      ["assignment.completed", "lesson.completed"],
      async (data) => {
        await analyticsService.handleLearningEvent(data);
      }
    );

    await rabbitMQ.subscribeToEvents(
      "subscription.events",
      "analytics.subscription.events",
      ["course.enrolled", "subscription.terminated"],
      async (data) => {
        await analyticsService.handleSubscriptionEvent(data);
      }
    );

    await rabbitMQ.subscribeToEvents(
      "payment.events",
      "analytics.payment.events",
      ["payment.completed", "payment.failed"],
      async (data) => {
        await analyticsService.handlePaymentEvent(data);
      }
    );

    // Setup cron jobs
    setupCronJobs();

    app.listen(PORT, () => {
      console.log(`Reporting & Analytics Service running on port ${PORT}`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};

// Graceful shutdown
process.on("SIGTERM", async () => {
  console.log("SIGTERM received, shutting down gracefully");
  await rabbitMQ.close();
  await mongoose.connection.close();
  process.exit(0);
});

process.on("SIGINT", async () => {
  console.log("SIGINT received, shutting down gracefully");
  await rabbitMQ.close();
  await mongoose.connection.close();
  process.exit(0);
});

startServer();
