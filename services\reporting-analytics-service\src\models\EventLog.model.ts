import mongoose, { Document, Schema } from 'mongoose';

export interface EventLogDocument extends Document {
  eventType: string;
  eventData: any;
  userId?: string;
  courseId?: string;
  timestamp: Date;
  processed: boolean;
}

const EventLogSchema = new Schema<EventLogDocument>(
  {
    eventType: { type: String, required: true, index: true },
    eventData: { type: Schema.Types.Mixed, required: true },
    userId: { type: String, index: true },
    courseId: { type: String, index: true },
    timestamp: { type: Date, required: true, default: Date.now, index: true },
    processed: { type: Boolean, default: false, index: true },
  },
  {
    timestamps: false, // We use our own timestamp field
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v;
        return ret;
      }
    }
  }
);

// TTL index to auto-delete old events after 90 days
EventLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 7776000 });

// Compound indexes for efficient querying
EventLogSchema.index({ eventType: 1, timestamp: -1 });
EventLogSchema.index({ userId: 1, timestamp: -1 });
EventLogSchema.index({ processed: 1, timestamp: 1 });

export const EventLogModel = mongoose.model<EventLogDocument>('EventLog', EventLogSchema);