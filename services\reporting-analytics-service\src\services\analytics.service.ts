import { EventLogModel } from '../models/EventLog.model';
import { AnalyticsModel } from '../models/Analytics.model';
import axios from 'axios';

export class AnalyticsService {
  private authServiceUrl = process.env.AUTH_SERVICE_URL || 'http://localhost:3001';
  private courseServiceUrl = process.env.COURSE_SERVICE_URL || 'http://localhost:3002';
  private paymentServiceUrl = process.env.PAYMENT_SERVICE_URL || 'http://localhost:3003';

  // Event handlers
  async handleUserEvent(eventData: any) {
    try {
      const eventLog = new EventLogModel({
        eventType: eventData.type,
        eventData: eventData.data,
        userId: eventData.data.firebaseUid,
        timestamp: eventData.timestamp || new Date(),
      });

      await eventLog.save();
      console.log(`User event logged: ${eventData.type}`);

    } catch (error) {
      console.error('Error handling user event:', error);
    }
  }

  async handleLearningEvent(eventData: any) {
    try {
      const eventLog = new EventLogModel({
        eventType: eventData.type,
        eventData: eventData.data,
        userId: eventData.data.studentId,
        courseId: eventData.data.courseId,
        timestamp: eventData.timestamp || new Date(),
      });

      await eventLog.save();
      console.log(`Learning event logged: ${eventData.type}`);

    } catch (error) {
      console.error('Error handling learning event:', error);
    }
  }

  async handleSubscriptionEvent(eventData: any) {
    try {
      const eventLog = new EventLogModel({
        eventType: eventData.type,
        eventData: eventData.data,
        userId: eventData.data.studentId,
        courseId: eventData.data.courseId,
        timestamp: eventData.timestamp || new Date(),
      });

      await eventLog.save();
      console.log(`Subscription event logged: ${eventData.type}`);

    } catch (error) {
      console.error('Error handling subscription event:', error);
    }
  }

  async handlePaymentEvent(eventData: any) {
    try {
      const eventLog = new EventLogModel({
        eventType: eventData.type,
        eventData: eventData.data,
        userId: eventData.data.studentId,
        courseId: eventData.data.courseId,
        timestamp: eventData.timestamp || new Date(),
      });

      await eventLog.save();
      console.log(`Payment event logged: ${eventData.type}`);

    } catch (error) {
      console.error('Error handling payment event:', error);
    }
  }

  // Data aggregation methods
  async aggregateHourlyData() {
    try {
      const now = new Date();
      const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      // Process unprocessed events from the last hour
      const events = await EventLogModel.find({
        timestamp: { $gte: hourAgo, $lte: now },
        processed: false
      });

      console.log(`Processing ${events.length} events for hourly aggregation`);

      // Mark events as processed
      await EventLogModel.updateMany(
        { _id: { $in: events.map(e => e._id) } },
        { $set: { processed: true } }
      );

      console.log('Hourly data aggregation completed');

    } catch (error) {
      console.error('Error in hourly data aggregation:', error);
    }
  }

  async aggregateDailyData() {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Fetch data from other services
      const [userStats, courseStats, paymentStats] = await Promise.all([
        this.fetchUserStats(),
        this.fetchCourseStats(),
        this.fetchPaymentStats()
      ]);

      // Check if analytics for today already exists
      const existingAnalytics = await AnalyticsModel.findOne({
        date: today,
        type: 'daily'
      });

      if (existingAnalytics) {
        // Update existing analytics
        Object.assign(existingAnalytics, {
          ...userStats,
          ...courseStats,
          ...paymentStats,
          updatedAt: new Date()
        });
        await existingAnalytics.save();
      } else {
        // Create new analytics
        const analytics = new AnalyticsModel({
          date: today,
          type: 'daily',
          ...userStats,
          ...courseStats,
          ...paymentStats
        });
        await analytics.save();
      }

      console.log('Daily data aggregation completed');

    } catch (error) {
      console.error('Error in daily data aggregation:', error);
    }
  }

  async generateWeeklyReports() {
    try {
      const today = new Date();
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - 7);
      weekStart.setHours(0, 0, 0, 0);

      // Aggregate weekly data from daily analytics
      const dailyAnalytics = await AnalyticsModel.find({
        date: { $gte: weekStart, $lt: today },
        type: 'daily'
      });

      if (dailyAnalytics.length > 0) {
        const weeklyData = this.aggregateWeeklyFromDaily(dailyAnalytics);
        
        const weeklyAnalytics = new AnalyticsModel({
          date: weekStart,
          type: 'weekly',
          ...weeklyData
        });

        await weeklyAnalytics.save();
        console.log('Weekly report generated');
      }

    } catch (error) {
      console.error('Error generating weekly reports:', error);
    }
  }

  private async fetchUserStats() {
    try {
      // In a real implementation, this would make API calls to the auth service
      // For now, we'll return mock data
      return {
        totalUsers: 1000,
        newUsers: 50,
        activeUsers: 800,
        studentCount: 900,
        tutorCount: 100
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      return {
        totalUsers: 0,
        newUsers: 0,
        activeUsers: 0,
        studentCount: 0,
        tutorCount: 0
      };
    }
  }

  private async fetchCourseStats() {
    try {
      // Mock data - in real implementation, query course service
      return {
        totalCourses: 200,
        publishedCourses: 150,
        newCourses: 10,
        totalEnrollments: 5000,
        newEnrollments: 200
      };
    } catch (error) {
      console.error('Error fetching course stats:', error);
      return {
        totalCourses: 0,
        publishedCourses: 0,
        newCourses: 0,
        totalEnrollments: 0,
        newEnrollments: 0
      };
    }
  }

  private async fetchPaymentStats() {
    try {
      // Mock data - in real implementation, query payment service
      return {
        totalRevenue: 50000000, // IDR
        completedPayments: 180,
        failedPayments: 20,
        averageOrderValue: 250000
      };
    } catch (error) {
      console.error('Error fetching payment stats:', error);
      return {
        totalRevenue: 0,
        completedPayments: 0,
        failedPayments: 0,
        averageOrderValue: 0
      };
    }
  }

  private aggregateWeeklyFromDaily(dailyAnalytics: any[]): any {
    return dailyAnalytics.reduce((acc, day) => ({
      totalUsers: Math.max(acc.totalUsers, day.totalUsers),
      newUsers: acc.newUsers + day.newUsers,
      activeUsers: Math.max(acc.activeUsers, day.activeUsers),
      studentCount: Math.max(acc.studentCount, day.studentCount),
      tutorCount: Math.max(acc.tutorCount, day.tutorCount),
      totalCourses: Math.max(acc.totalCourses, day.totalCourses),
      publishedCourses: Math.max(acc.publishedCourses, day.publishedCourses),
      newCourses: acc.newCourses + day.newCourses,
      totalEnrollments: Math.max(acc.totalEnrollments, day.totalEnrollments),
      newEnrollments: acc.newEnrollments + day.newEnrollments,
      totalRevenue: acc.totalRevenue + day.totalRevenue,
      completedPayments: acc.completedPayments + day.completedPayments,
      failedPayments: acc.failedPayments + day.failedPayments,
    }), {
      totalUsers: 0, newUsers: 0, activeUsers: 0, studentCount: 0, tutorCount: 0,
      totalCourses: 0, publishedCourses: 0, newCourses: 0, totalEnrollments: 0,
      newEnrollments: 0, totalRevenue: 0, completedPayments: 0, failedPayments: 0
    });
  }
}