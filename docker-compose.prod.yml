version: '3.8'

services:
  # MongoDB databases
  mongodb-auth:
    image: mongo:7.0
    container_name: timecourse-mongodb-auth-prod
    restart: always
    environment:
      - MONGO_INITDB_DATABASE=timecourse_auth
      - MONGO_INITDB_ROOT_USERNAME=${MONGODB_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGODB_ROOT_PASSWORD}
    volumes:
      - mongodb_auth_data:/data/db
    networks:
      - timecourse-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  mongodb-courses:
    image: mongo:7.0
    container_name: timecourse-mongodb-courses-prod
    restart: always
    environment:
      - MONGO_INITDB_DATABASE=timecourse_courses
      - MONGO_INITDB_ROOT_USERNAME=${MONGODB_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGODB_ROOT_PASSWORD}
    volumes:
      - mongodb_courses_data:/data/db
    networks:
      - timecourse-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  mongodb-payments:
    image: mongo:7.0
    container_name: timecourse-mongodb-payments-prod
    restart: always
    environment:
      - MONGO_INITDB_DATABASE=timecourse_payments
      - MONGO_INITDB_ROOT_USERNAME=${MONGODB_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGODB_ROOT_PASSWORD}
    volumes:
      - mongodb_payments_data:/data/db
    networks:
      - timecourse-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  mongodb-analytics:
    image: mongo:7.0
    container_name: timecourse-mongodb-analytics-prod
    restart: always
    environment:
      - MONGO_INITDB_DATABASE=timecourse_analytics
      - MONGO_INITDB_ROOT_USERNAME=${MONGODB_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGODB_ROOT_PASSWORD}
    volumes:
      - mongodb_analytics_data:/data/db
    networks:
      - timecourse-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: timecourse-rabbitmq-prod
    restart: always
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USERNAME}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - timecourse-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Auth & User Service
  auth-user-service:
    build:
      context: .
      dockerfile: services/auth-user-service/Dockerfile
    container_name: timecourse-auth-service-prod
    restart: always
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MONGODB_URI=mongodb://${MONGODB_ROOT_USERNAME}:${MONGODB_ROOT_PASSWORD}@mongodb-auth:27017/timecourse_auth?authSource=admin
      - RABBITMQ_URL=amqp://${RABBITMQ_USERNAME}:${RABBITMQ_PASSWORD}@rabbitmq:5672
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
    depends_on:
      - mongodb-auth
      - rabbitmq
    networks:
      - timecourse-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Course & Learning Service
  course-learning-service:
    build:
      context: .
      dockerfile: services/course-learning-service/Dockerfile
    container_name: timecourse-course-service-prod
    restart: always
    environment:
      - NODE_ENV=production
      - PORT=3002
      - MONGODB_URI=mongodb://${MONGODB_ROOT_USERNAME}:${MONGODB_ROOT_PASSWORD}@mongodb-courses:27017/timecourse_courses?authSource=admin
      - RABBITMQ_URL=amqp://${RABBITMQ_USERNAME}:${RABBITMQ_PASSWORD}@rabbitmq:5672
      - BUNNY_NET_API_KEY=${BUNNY_NET_API_KEY}
      - BUNNY_NET_LIBRARY_ID=${BUNNY_NET_LIBRARY_ID}
      - BUNNY_NET_STREAM_URL=${BUNNY_NET_STREAM_URL}
    depends_on:
      - mongodb-courses
      - rabbitmq
    networks:
      - timecourse-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Payment & Subscription Service
  payment-subscription-service:
    build:
      context: .
      dockerfile: services/payment-subscription-service/Dockerfile
    container_name: timecourse-payment-service-prod
    restart: always
    environment:
      - NODE_ENV=production
      - PORT=3003
      - MONGODB_URI=mongodb://${MONGODB_ROOT_USERNAME}:${MONGODB_ROOT_PASSWORD}@mongodb-payments:27017/timecourse_payments?authSource=admin
      - RABBITMQ_URL=amqp://${RABBITMQ_USERNAME}:${RABBITMQ_PASSWORD}@rabbitmq:5672
      - XENDIT_API_KEY=${XENDIT_API_KEY}
      - XENDIT_WEBHOOK_TOKEN=${XENDIT_WEBHOOK_TOKEN}
      - XENDIT_BASE_URL=https://api.xendit.co
      - FRONTEND_URL=${FRONTEND_URL}
    depends_on:
      - mongodb-payments
      - rabbitmq
    networks:
      - timecourse-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Reporting & Analytics Service
  reporting-analytics-service:
    build:
      context: .
      dockerfile: services/reporting-analytics-service/Dockerfile
    container_name: timecourse-analytics-service-prod
    restart: always
    environment:
      - NODE_ENV=production
      - PORT=3004
      - MONGODB_URI=mongodb://${MONGODB_ROOT_USERNAME}:${MONGODB_ROOT_PASSWORD}@mongodb-analytics:27017/timecourse_analytics?authSource=admin
      - RABBITMQ_URL=amqp://${RABBITMQ_USERNAME}:${RABBITMQ_PASSWORD}@rabbitmq:5672
      - AUTH_SERVICE_URL=http://auth-user-service:3001
      - COURSE_SERVICE_URL=http://course-learning-service:3002
      - PAYMENT_SERVICE_URL=http://payment-subscription-service:3003
    depends_on:
      - mongodb-analytics
      - rabbitmq
    networks:
      - timecourse-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # API Gateway (Nginx)
  api-gateway:
    image: nginx:alpine
    container_name: timecourse-api-gateway-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - auth-user-service
      - course-learning-service
      - payment-subscription-service
      - reporting-analytics-service
    networks:
      - timecourse-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  mongodb_auth_data:
  mongodb_courses_data:
  mongodb_payments_data:
  mongodb_analytics_data:
  rabbitmq_data:

networks:
  timecourse-network:
    driver: bridge