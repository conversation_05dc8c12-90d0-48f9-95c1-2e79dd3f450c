# Time Course - English Online Course Platform

Time Course adalah platform pembelajaran online berbasis langganan yang khusus untuk persiapan tes kemampuan bahasa Inggris seperti IELTS, TOEFL, dll. Platform ini menggunakan sistem evaluasi yang ketat dan mekanisme pembinaan untuk siswa yang gagal.

## 🏗️ Arsitektur Microservices

Platform ini dibangun menggunakan arsitektur microservices dengan 4 layanan utama:

1. **Auth & User Service** (Port 3001)
   - Autentikasi dengan Firebase Auth
   - Manajemen pengguna (Student, Tutor, Admin)
   - Approval tutor oleh admin

2. **Course & Learning Service** (Port 3002)
   - Manajemen kursus dan materi pembelajaran
   - Upload video menggunakan Bunny.net
   - Sistem assignment interaktif
   - Sesi coaching untuk siswa yang gagal

3. **Payment & Subscription Service** (Port 3003)
   - Integrasi pembayaran dengan Xendit
   - Manajemen langganan dengan batas waktu
   - Sistem failure tracking (3 kegagalan = terminasi)
   - Webhook handler untuk notifikasi pembayaran

4. **Reporting & Analytics Service** (Port 3004)
   - Pengumpulan data dari semua service
   - Dashboard analitik untuk admin dan tutor
   - Laporan performa dan keuangan
   - Cron jobs untuk agregasi data

## 🛠️ Tech Stack

- **Backend:** Node.js + Express.js + TypeScript
- **Database:** MongoDB (database terpisah per service)
- **Message Broker:** RabbitMQ
- **Authentication:** Firebase Auth
- **Payment Gateway:** Xendit
- **Video Streaming:** Bunny.net
- **Containerization:** Docker + Docker Compose
- **API Gateway:** Nginx dengan rate limiting

## 🚀 Quick Start

### Prerequisites

- Docker & Docker Compose
- Node.js 18+ (untuk development)
- Firebase project dengan service account
- Xendit account untuk payment gateway
- Bunny.net account untuk video streaming

### Environment Setup

1. Copy file environment template:
```bash
cp .env.example .env
```

2. Isi konfigurasi yang diperlukan di file `.env`:
   - Firebase credentials
   - Xendit API keys
   - Bunny.net configuration
   - Database passwords

### Development

```bash
# Install all dependencies
npm run install:all

# Start development environment
npm run dev

# Or manually start with docker-compose
docker-compose -f docker-compose.dev.yml up --build
```

### Production

```bash
# Start production environment
npm run prod

# Or manually
docker-compose -f docker-compose.prod.yml up --build -d
```

## 📋 API Endpoints

### Auth & User Service (Port 3001)
```
POST   /api/auth/register     - Register new user
POST   /api/auth/login        - Login user
GET    /api/auth/profile      - Get user profile
PUT    /api/auth/profile      - Update profile
GET    /api/users             - Get all users (admin)
POST   /api/tutors            - Create tutor account (admin)
GET    /api/tutors            - Get all tutors (admin)
```

### Course & Learning Service (Port 3002)
```
GET    /api/courses           - Get course catalog
POST   /api/courses           - Create course (tutor)
GET    /api/courses/:id       - Get course details
PUT    /api/courses/:id       - Update course (tutor)
POST   /api/lessons           - Create lesson (tutor)
POST   /api/assignments/submit - Submit assignment (student)
GET    /api/coaching/available - Get available coaching sessions
```

### Payment & Subscription Service (Port 3003)
```
POST   /api/payments/create   - Create payment
GET    /api/payments/my-payments - Get user payments
GET    /api/subscriptions/my-subscriptions - Get user subscriptions
POST   /api/webhooks/xendit   - Xendit webhook handler
GET    /api/subscriptions/admin/failed-students - Get failed students
```

### Analytics & Reporting Service (Port 3004)
```
GET    /api/analytics/dashboard - Get dashboard analytics (admin)
GET    /api/analytics/users     - Get user analytics (admin)
GET    /api/analytics/revenue   - Get revenue analytics (admin)
GET    /api/reports/platform-overview - Generate platform report
GET    /api/reports/tutor/performance - Get tutor performance (tutor)
```

## 🔥 Key Features

### Strict Progress Enforcement
- Siswa harus lulus assignment untuk melanjutkan
- 3 kegagalan assignment = terminasi akses kursus otomatis
- Assignment terintegrasi dengan video pada timestamp tertentu

### Mandatory Pacing System
- Sistem menghitung pace berdasarkan durasi kursus
- Warning jika siswa tertinggal dari jadwal yang diharapkan
- Dashboard menampilkan progress bar dan status pace

### Free Remedial Coaching
- Siswa yang di-terminasi otomatis eligible untuk sesi coaching gratis
- Tutor dapat membuat sesi coaching dengan link meeting
- Sistem invitasi otomatis untuk siswa yang gagal

### Admin-Approved Tutors
- Tutor tidak bisa registrasi sendiri
- Harus dibuat dan disetujui oleh admin
- Sistem tracking approval dengan timestamp

### Secure Video Streaming
- Integrasi dengan Bunny.net untuk streaming HLS/DASH
- Tidak ada download button untuk materi
- Video player dengan kontrol assignment

## 🔐 Security Features

- JWT token validation dengan Firebase Admin SDK
- Role-based access control (RBAC)
- Rate limiting pada API Gateway
- CORS protection
- Request validation dengan Joi
- Secure webhook validation

## 📊 Monitoring & Analytics

- Real-time event logging dengan RabbitMQ
- Automated data aggregation dengan cron jobs
- Comprehensive dashboard untuk admin
- Tutor-specific analytics dan reporting
- Revenue dan performance tracking

## 🐳 Docker Services

- **MongoDB:** 4 instance terpisah untuk setiap service
- **RabbitMQ:** Message broker dengan management UI
- **Nginx:** API Gateway dengan load balancing
- **Services:** 4 microservices dalam container terpisah

## 📈 Scalability

- Horizontal scaling per service
- Database sharding per domain
- Event-driven architecture dengan RabbitMQ
- Stateless services dengan external session storage
- CDN integration untuk static assets

## 🔄 CI/CD Pipeline

GitHub Actions workflow untuk:
- Automated testing per service
- Docker image building dan pushing
- Deployment ke production
- Notification system

## 🤝 Contributing

1. Fork repository
2. Buat feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push ke branch (`git push origin feature/amazing-feature`)
5. Buat Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

Untuk support dan pertanyaan, hubungi tim development atau buat issue di repository GitHub.